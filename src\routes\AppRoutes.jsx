import { Routes, Route } from 'react-router-dom';

// Import page components
import Home from '../pages/Home';
import About from '../pages/About';
import Contact from '../pages/Contact';
import Products from '../pages/Products';
import Auth from '../pages/Auth';

const AppRoutes = () => (
  <Routes>
    <Route path="/" element={<Home />} />
    <Route path="/about" element={<About />} />
    <Route path="/contact" element={<Contact />} />
    <Route path="/products" element={<Products />} />
    <Route path="/login" element={<Auth />} />
    <Route path="/signup" element={<Auth />} />
  </Routes>
);

export default AppRoutes;
